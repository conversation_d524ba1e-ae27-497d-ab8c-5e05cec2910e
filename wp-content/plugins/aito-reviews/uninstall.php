<?php
/**
 * AITO Reviews Plugin Uninstall Script
 * 
 * This file is executed when the plugin is uninstalled (deleted) from WordPress.
 * It cleans up all plugin data from the database.
 */

// Prevent direct access
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Clear scheduled cron job
$timestamp = wp_next_scheduled('aito_reviews_fetch_data');
if ($timestamp) {
    wp_unschedule_event($timestamp, 'aito_reviews_fetch_data');
}

// List of plugin options to remove
$options_to_remove = array(
    'aito_reviews_endpoint_url',
    'aito_reviews_username',
    'aito_reviews_password',
    'aito_reviews_company_id',
    'aito_reviews_cron_interval',
    'aito_reviews_enabled',
    'aito_reviews_score',
    'aito_reviews_total',
    'aito_reviews_last_update',
    'aito_reviews_last_error',
    'aito_reviews_data',
    'aito_reviews_raw_response',
    'aito_reviews_log'
);

// Remove all plugin options
foreach ($options_to_remove as $option) {
    delete_option($option);
}

// For multisite installations, remove options from all sites
if (is_multisite()) {
    global $wpdb;
    
    // Get all blog IDs
    $blog_ids = $wpdb->get_col("SELECT blog_id FROM $wpdb->blogs");
    
    foreach ($blog_ids as $blog_id) {
        switch_to_blog($blog_id);
        
        // Clear cron job for this site
        $timestamp = wp_next_scheduled('aito_reviews_fetch_data');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'aito_reviews_fetch_data');
        }
        
        // Remove options for this site
        foreach ($options_to_remove as $option) {
            delete_option($option);
        }
        
        restore_current_blog();
    }
}

// Clean up debug files and .htaccess protection
$upload_dir = wp_upload_dir();
$debug_file = $upload_dir['basedir'] . '/aito-debug.log';
$htaccess_file = $upload_dir['basedir'] . '/.htaccess';

// Remove debug log file
if (file_exists($debug_file)) {
    unlink($debug_file);
}

// Remove our .htaccess protection (but preserve other rules)
if (file_exists($htaccess_file)) {
    $content = file_get_contents($htaccess_file);
    $marker_start = '# BEGIN AITO Reviews Protection';
    $marker_end = '# END AITO Reviews Protection';

    $start_pos = strpos($content, $marker_start);
    if ($start_pos !== false) {
        $end_pos = strpos($content, $marker_end);
        if ($end_pos !== false) {
            // Remove our section including the end marker and newline
            $end_pos += strlen($marker_end) + 1;
            $new_content = substr($content, 0, $start_pos) . substr($content, $end_pos);

            // If the file is now empty or only whitespace, remove it
            if (trim($new_content) === '') {
                unlink($htaccess_file);
            } else {
                file_put_contents($htaccess_file, $new_content, LOCK_EX);
            }
        }
    }
}

// Clear any cached data
wp_cache_flush();

// Log the uninstall (if debug is enabled)
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('AITO Reviews Plugin: Uninstalled and cleaned up all data');
}
